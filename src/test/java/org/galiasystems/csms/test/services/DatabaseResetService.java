package org.galiasystems.csms.test.services;

import io.quarkus.arc.profile.IfBuildProfile;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import org.flywaydb.core.Flyway;

import java.util.logging.Logger;

/**
 * Service responsible for resetting the database during tests.
 * This service is only available in the test profile.
 */
@ApplicationScoped
@IfBuildProfile("test")
public class DatabaseResetService {

    private static final Logger logger = Logger.getLogger(DatabaseResetService.class.getName());

    @Inject
    Flyway flyway;

    /**
     * Resets the database by:
     * 1. Cleaning the database
     * 2. Running Flyway migrations
     *
     * @return Uni<Void> that completes when the reset is finished
     */
    public Uni<Void> resetDatabase() {
        logger.info("Starting database reset...");

        return Uni.createFrom().item(() -> {
            try {
                // Clean the database using Flyway
                logger.info("Cleaning database...");
                flyway.clean();

                // Run migrations
                logger.info("Running Flyway migrations...");
                flyway.migrate();

                logger.info("Database reset completed successfully");
                return null;
            } catch (Exception e) {
                logger.severe("Failed to reset database: " + e.getMessage());
                throw new RuntimeException("Database reset failed", e);
            }
        });
    }

}