package org.galiasystems.csms.test.utils;

import io.quarkus.arc.Arc;
import org.galiasystems.csms.test.services.DatabaseResetService;

import java.util.logging.Logger;

/**
 * Utility class for manually resetting the database in tests.
 */
public class DatabaseResetUtil {

    private static final Logger logger = Logger.getLogger(DatabaseResetUtil.class.getName());

    /**
     * Resets the database by cleaning all data and re-running Flyway migrations.
     *
     * @throws RuntimeException if the database reset fails
     */
    public static void resetDatabase() {
        logger.info("Manually resetting database...");

        try {
            // Get the DatabaseResetService from the CDI container
            var container = Arc.container();
            if (container == null) {
                throw new RuntimeException("CDI container not available - this method must be called from within a Quarkus test");
            }

            var serviceInstance = container.instance(DatabaseResetService.class);
            if (!serviceInstance.isAvailable()) {
                throw new RuntimeException("DatabaseResetService not available in CDI container");
            }

            DatabaseResetService resetService = serviceInstance.get();

            resetService.resetDatabase()
                    .await()
                    .indefinitely();

            logger.info("Database reset completed successfully");

        } catch (Exception e) {
            logger.severe("Failed to reset database: " + e.getMessage());
            throw new RuntimeException("Database reset failed", e);
        }
    }
}