-- Create sequence for authorization_code_request_data table
CREATE SEQUENCE IF NOT EXISTS authorization_code_request_data_id_seq START WITH 1 INCREMENT BY 1;

-- Create authorization_code_request_data table
CREATE TABLE IF NOT EXISTS authorization_code_request_data (
    id BIGINT PRIMARY KEY DEFAULT nextval('authorization_code_request_data_id_seq'),
    session_id VARCHAR(255) NOT NULL UNIQUE,
    code_verifier VARCHAR(255) NOT NULL,
    version INTEGER NOT NULL DEFAULT 0
);
