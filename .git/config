[core]
	repositoryformatversion = 0
	filemode = false
	bare = false
	logallrefupdates = true
	symlinks = false
	ignorecase = true
[remote "origin"]
	url = **************:galiasystems/smart-charge.git
	fetch = +refs/heads/*:refs/remotes/origin/*
[branch "main"]
	remote = origin
	merge = refs/heads/main
	vscode-merge-base = origin/main
	vscode-merge-base = origin/main
[branch "feat/add-new-entities"]
	remote = origin
	merge = refs/heads/feat/add-new-entities
[branch "feature/create_charging_station"]
	remote = origin
	merge = refs/heads/feature/create_charging_station
[branch "feat/register_charging_station"]
	remote = origin
	merge = refs/heads/feat/register_charging_station
[branch "feat/update_charging_station"]
	remote = origin
	merge = refs/heads/feat/update_charging_station
[branch "fix/typos"]
	remote = origin
	merge = refs/heads/fix/typos
[branch "fix/db_init"]
	remote = origin
	merge = refs/heads/fix/db_init
[branch "feat/subprotocol_handling"]
	remote = origin
	merge = refs/heads/feat/subprotocol_handling
[branch "feature/bootnotification_1_6"]
	remote = origin
	merge = refs/heads/feature/bootnotification_1_6
[branch "feature/StartTransaction_v1.6"]
	remote = origin
	merge = refs/heads/feature/StartTransaction_v1.6
[branch "feature/remote_transaction"]
	remote = origin
	merge = refs/heads/feature/remote_transaction
[branch "feature/remote_start_stop_transaction"]
	remote = origin
	merge = refs/heads/feature/remote_start_stop_transaction
[branch "feature/sonar"]
	remote = origin
	merge = refs/heads/feature/sonar
[branch "feature/meter_values"]
	remote = origin
	merge = refs/heads/feature/meter_values
[branch "fix/meter_values"]
	remote = origin
	merge = refs/heads/fix/meter_values
[branch "feature/change_configuration"]
	remote = origin
	merge = refs/heads/feature/change_configuration
	vscode-merge-base = origin/main
[branch "feature/authorization_ocpp1_6"]
	vscode-merge-base = origin/main
	remote = origin
	merge = refs/heads/feature/authorization_ocpp1_6
[branch "feature/authorization_ocpp2_0_1"]
	vscode-merge-base = origin/main
	remote = origin
	merge = refs/heads/feature/authorization_ocpp2_0_1
[branch "feature/add_charging_station_admin_to_new_cs"]
	vscode-merge-base = origin/main
	remote = origin
	merge = refs/heads/feature/add_charging_station_admin_to_new_cs
[branch "feature/update_location"]
	remote = origin
	merge = refs/heads/feature/update_location
	vscode-merge-base = origin/main
[branch "fix/typo"]
	remote = origin
	merge = refs/heads/fix/typo
[branch "feat/getDiagnostics"]
	remote = origin
	merge = refs/heads/feat/getDiagnostics
[branch "feat/data_from_bootnotification"]
	remote = origin
	merge = refs/heads/feat/data_from_bootnotification
[branch "feature/status_update_gql_subscription"]
	remote = origin
	merge = refs/heads/feature/status_update_gql_subscription
[branch "fix/meter_values_fix"]
	remote = origin
	merge = refs/heads/fix/meter_values_fix
[branch "feature/charging_stations_by_location"]
	remote = origin
	merge = refs/heads/feature/charging_stations_by_location
[branch "feature/meter_values_2"]
	remote = origin
	merge = refs/heads/feature/meter_values_2
[branch "feature/transaction"]
	remote = origin
	merge = refs/heads/feature/transaction
[branch "feature/charging_session"]
	remote = origin
	merge = refs/heads/feature/charging_session
[branch "feature/ocpi_session"]
	remote = origin
	merge = refs/heads/feature/ocpi_session
[branch "feature/session_rest"]
	remote = origin
	merge = refs/heads/feature/session_rest
[branch "feature/sessions_rest_2"]
	remote = origin
	merge = refs/heads/feature/sessions_rest_2
[branch "feature/meter_values_example_data_for_chart"]
	remote = origin
	merge = refs/heads/feature/meter_values_example_data_for_chart
[branch "feature/typesafe_filtering"]
	remote = origin
	merge = refs/heads/feature/typesafe_filtering
[branch "fix/test"]
	vscode-merge-base = origin/main
	vscode-merge-base = origin/main
	remote = origin
	merge = refs/heads/fix/test
[branch "fix/test2"]
	remote = origin
	merge = refs/heads/fix/test2
